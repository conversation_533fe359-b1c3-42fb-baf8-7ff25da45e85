# 时序违例插件搜索过滤功能

## 功能概述

为时序违例确认插件的违例表格增加了搜索过滤功能，用户可以通过输入层级路径关键字来快速过滤和查找相关的违例记录。

## 功能特性

### 🔍 实时搜索过滤
- **实时响应**：用户输入搜索关键字时，表格会实时更新显示匹配的违例
- **模糊匹配**：支持层级路径的模糊匹配，不需要输入完整路径
- **大小写不敏感**：搜索时忽略大小写差异

### 📊 搜索统计显示
- **结果统计**：显示"搜索结果: X/Y 条"，清楚展示过滤结果
- **即时反馈**：搜索条件变化时统计信息实时更新

### 🧹 便捷操作
- **一键清除**：提供"清除"按钮快速清空搜索条件
- **自动恢复**：清除搜索后自动恢复显示所有违例记录

## UI界面变化

### 控制面板新增搜索区域
在原有控制面板的第三行新增了搜索功能区域：

```
┌─────────────────────────────────────────────────────────────┐
│ 搜索层级路径: [输入框________________] [清除] 搜索结果: X/Y 条 │
└─────────────────────────────────────────────────────────────┘
```

- **搜索输入框**：占据主要空间，提供清晰的提示文本
- **清除按钮**：紧邻输入框，方便快速清除搜索条件
- **统计标签**：显示搜索结果统计，使用灰色字体不干扰主要内容

## 技术实现

### 数据结构设计
```python
# 新增实例变量
self.original_violations = []  # 保存原始违例数据
self.filtered_violations = []  # 保存过滤后的违例数据
self.search_filter_text = ""   # 当前搜索过滤文本
```

### 核心方法

#### 1. 搜索文本变更处理
```python
def on_search_text_changed(self, text: str):
    """搜索文本变更处理"""
    self.search_filter_text = text.strip()
    
    if not self.search_filter_text:
        self.clear_search_filter()
        return
    
    self.apply_search_filter()
```

#### 2. 应用搜索过滤
```python
def apply_search_filter(self):
    """应用搜索过滤"""
    search_text = self.search_filter_text.lower()
    
    # 过滤违例数据
    filtered_violations = []
    for violation in self.original_violations:
        # 跳过分组标识行
        if violation.get('is_group_header', False):
            continue
            
        # 获取层级路径进行匹配
        hier_path = violation.get('hier', '').lower()
        if search_text in hier_path:
            filtered_violations.append(violation)
    
    # 更新过滤后的数据并刷新显示
    self.filtered_violations = filtered_violations
    self.current_violations = filtered_violations
    self._refresh_table_display()
```

#### 3. 清除搜索过滤
```python
def clear_search_filter(self):
    """清除搜索过滤"""
    self.search_filter_text = ""
    self.search_edit.clear()
    self.search_stats_label.setText("")
    
    # 恢复原始数据
    if self.original_violations:
        self.current_violations = self.original_violations.copy()
        self._refresh_table_display()
```

### 数据生命周期管理

#### 数据加载时
- 备份原始违例数据到 `original_violations`
- 清除之前的搜索条件和过滤结果
- 如果有搜索条件，自动应用过滤

#### 数据清除时
- 同时清除原始数据、过滤数据和搜索条件
- 重置搜索统计显示

#### 模式切换时
- 单文件模式 ↔ 批量处理模式切换时自动清除搜索条件
- 确保数据状态的一致性

## 使用场景

### 1. 快速定位特定模块的违例
```
搜索: "cpu" → 显示所有CPU相关的违例
搜索: "memory" → 显示所有内存相关的违例
```

### 2. 按层级深度过滤
```
搜索: "core0" → 显示core0相关的违例
搜索: "alu" → 显示所有ALU相关的违例
```

### 3. 精确路径匹配
```
搜索: "top.cpu.core0.alu" → 显示该路径下的所有违例
```

## 性能优化

### 高效过滤算法
- 使用字符串包含匹配，避免复杂的正则表达式
- 预先转换为小写，减少重复转换开销
- 跳过分组标识行，只处理真实违例数据

### 内存管理
- 原始数据和过滤数据分别存储，避免数据丢失
- 及时清理不需要的数据，防止内存泄漏
- 支持大数据集的高性能表格显示

### 响应性优化
- 实时搜索响应，无需点击搜索按钮
- 搜索结果统计实时更新
- 表格刷新优化，只更新显示不重新加载数据

## 兼容性

### 表格类型兼容
- **标准表格**：小数据集使用标准QTableWidget
- **高性能表格**：大数据集自动切换到高性能表格
- **搜索过滤**：两种表格类型都完全支持搜索功能

### 模式兼容
- **单文件模式**：完全支持搜索过滤
- **批量处理模式**：完全支持搜索过滤，包括分组标识行的正确处理

### 功能兼容
- 搜索功能不影响现有的确认、导出、历史应用等功能
- 过滤后的数据可以正常进行批量操作
- 与性能优化系统完全兼容

## 用户体验

### 直观的界面设计
- 搜索框位置醒目，易于发现和使用
- 提示文本清晰，用户一看就懂如何使用
- 统计信息实时更新，让用户了解搜索效果

### 便捷的操作流程
1. 在搜索框中输入关键字
2. 表格自动过滤显示匹配结果
3. 查看统计信息了解过滤效果
4. 点击"清除"按钮恢复所有数据

### 智能的功能设计
- 自动跳过分组标识行，只搜索真实违例
- 模糊匹配降低使用门槛
- 大小写不敏感提高搜索成功率

## 测试验证

创建了完整的测试脚本 `test_search_filter.py` 验证功能：
- 测试基本搜索功能
- 测试搜索结果统计
- 测试清除搜索功能
- 测试数据恢复功能

## 总结

搜索过滤功能的加入显著提升了时序违例插件的易用性，特别是在处理大量违例数据时，用户可以快速定位到感兴趣的违例记录，大大提高了工作效率。该功能设计简洁、性能优良、兼容性强，是对现有功能的有力补充。