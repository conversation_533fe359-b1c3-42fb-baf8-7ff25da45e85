# 用例状态过滤功能实现说明

## 功能概述
在回归批量扫描功能中增加了用例状态过滤功能，支持根据用例的PASS/FAIL状态进行过滤。

## 实现原理
- **状态判断规则**: 在vio_summary.log所在目录下，如果存在"sprd_log_pass.log"文件，则认为该用例状态为PASS，否则为FAIL
- **默认过滤**: 默认只显示PASS状态的用例，用户可以选择显示全部或只显示FAIL用例

## 修改的文件

### 1. regression_scanner.py
- **RegressionFileInfo**: 增加了`case_status`字段存储用例状态
- **RegressionScanResult**: 增加了`status_groups`字段按状态分组文件
- **_detect_case_status()**: 新增方法，检测用例状态
- **_parse_standard_structure()**: 更新解析方法，包含状态检测
- **_parse_flexible_structure()**: 更新解析方法，包含状态检测
- **_group_by_status()**: 新增方法，按状态分组文件
- 更新扫描结果创建，包含状态分组

### 2. regression_batch_manager.py
- **selection_filters**: 增加了'status'过滤器
- **get_status_list()**: 新增方法，获取状态列表
- **get_files_by_filters()**: 更新过滤逻辑，包含状态过滤
- **set_status_filter()**: 新增方法，设置状态过滤器

### 3. regression_batch_ui.py
- **UI布局**: 在过滤器组中增加"用例状态"下拉列表
- **默认设置**: 状态过滤器默认选择"PASS"
- **信号连接**: 连接状态过滤器的变更信号
- **update_filter_combos()**: 更新过滤器组合框，包含状态选项
- **apply_filters()**: 更新过滤器应用逻辑，包含状态过滤
- **文件表格**: 增加状态列，并根据状态设置背景色
- **文件树**: 在文件名中显示状态，并设置背景色
- **自动应用**: 扫描完成后自动应用PASS过滤器

## 用户界面变化

### 过滤器组
```
子系统: [下拉框]
工艺角: [下拉框]  
用例:   [下拉框]
用例状态: [PASS] (默认选择)
         [全部]
         [FAIL]
```

### 选中文件表格
增加了"状态"列，PASS用例显示绿色背景，FAIL用例显示红色背景。

### 文件树
文件节点显示格式: `seed_X (PASS/FAIL)`，并根据状态设置背景色。

## 使用方式

1. **默认行为**: 扫描完成后，自动只显示PASS状态的用例
2. **查看全部**: 将"用例状态"选择为"全部"，点击"应用过滤器"
3. **查看FAIL**: 将"用例状态"选择为"FAIL"，点击"应用过滤器"
4. **组合过滤**: 可以与子系统、工艺角、用例过滤器组合使用

## 测试验证
创建了测试脚本`test_case_status.py`验证功能正确性：
- 创建包含PASS和FAIL用例的测试目录结构
- 验证状态检测逻辑
- 验证状态分组功能

测试结果显示功能工作正常，能够正确识别用例状态并进行分组。