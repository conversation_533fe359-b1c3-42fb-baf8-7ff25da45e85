# 回归批量扫描问题修复报告

## 问题总结

本次修复解决了回归批量扫描功能中的三个关键问题：

1. **缓存问题**：新增用例未被扫描出来
2. **过滤器选择问题**：选中subsys时包含了被过滤的FAIL用例
3. **批量历史确认问题**：只有第一个文件应用了历史确认

## 修复详情

### 1. 缓存失效机制优化

**问题原因**：缓存机制过于激进，没有检查目录修改时间

**修复方案**：
- 在`start_scan()`方法中增加目录修改时间检查
- 如果目录修改时间晚于缓存时间，自动清除缓存并重新扫描
- 确保新增用例能被及时发现

**修改文件**：`plugins/user/timing_violation/regression_batch_ui.py`

```python
# 检查目录修改时间，如果目录有更新则强制重新扫描
try:
    current_dir_mtime = os.path.getmtime(regression_path)
    if self._is_scan_cached(cache_key):
        cached_result, cache_timestamp = self._scan_cache[cache_key]
        # 如果目录修改时间晚于缓存时间，则需要重新扫描
        if current_dir_mtime > cache_timestamp:
            print(f"目录已更新，清除缓存并重新扫描: {regression_path}")
            del self._scan_cache[cache_key]
```

### 2. 过滤器选择逻辑修复

**问题原因**：树选择逻辑没有考虑当前过滤器状态，选中父节点时会包含所有子节点

**修复方案**：
- 修改`_update_children_check_state()`方法
- 在收集文件时只选择过滤后的文件
- 跳过隐藏的项目

**修改文件**：`plugins/user/timing_violation/regression_batch_ui.py`

```python
def _update_children_check_state(self, item: QTreeWidgetItem, check_state: Qt.CheckState):
    """更新子项目的选择状态"""
    file_paths = []
    
    # 获取当前过滤后的文件路径集合
    filtered_files = self.batch_manager.get_files_by_filters()
    filtered_paths = {f.file_path for f in filtered_files}

    def collect_files(current_item):
        # 跳过隐藏的项目
        if current_item.isHidden():
            return
            
        item_data = current_item.data(0, Qt.UserRole)
        if item_data and item_data.get('type') == 'file':
            file_info = item_data.get('file_info')
            if file_info and file_info.file_path in filtered_paths:
                # 只选择过滤后的文件
                file_paths.append(file_info.file_path)
                current_item.setCheckState(0, check_state)
```

### 3. 批量历史确认逻辑重构

**问题原因**：
- 历史确认逻辑没有按来源分组处理
- 数据库保存逻辑有问题，导致后续文件的违例没有正确的数据库ID
- 手动应用历史确认不支持批量模式

**修复方案**：

#### 3.1 自动应用历史确认优化
- 按来源分组处理违例数据
- 对每个来源分别应用历史确认
- 改进数据库保存逻辑

**修改文件**：`plugins/user/timing_violation/main_window.py`

```python
def _auto_apply_historical_confirmations_for_batch(self):
    """自动应用历史确认记录（批量处理模式）"""
    # 按来源分组处理违例数据
    violations_by_source = {}
    for violation in self.current_violations:
        # 跳过分组标识行
        if violation.get('is_group_header', False):
            continue
            
        source_case = violation.get('source_case', '')
        source_corner = violation.get('source_corner', '')
        source_key = f"{source_case}_{source_corner}"
        
        if source_key not in violations_by_source:
            violations_by_source[source_key] = []
        violations_by_source[source_key].append(violation)

    total_applied = 0
    
    # 对每个来源的违例分别应用历史确认
    for source_key, source_violations in violations_by_source.items():
        # ... 处理逻辑
```

#### 3.2 数据库保存逻辑改进
- 跳过分组标识行
- 先清除旧数据避免重复
- 验证保存结果

```python
def _save_batch_violations_to_database(self, violations: List[Dict]):
    """将批量处理的违例数据保存到数据库"""
    # 为每个来源文件分别保存违例数据
    violations_by_source = {}
    for violation in violations:
        # 跳过分组标识行
        if violation.get('is_group_header', False):
            continue
        # ... 分组逻辑
    
    for (case_name, corner_name, file_path), case_violations in violations_by_source.items():
        # 先清除该case的旧数据，避免重复
        self.data_model.clear_case_data(case_name, corner_name)
        
        # 保存新数据
        self.data_model.add_violations(
            violations=case_violations,
            case_name=case_name,
            corner=corner_name,
            file_path=file_path
        )
```

#### 3.3 手动应用历史确认支持批量模式
- 检测批量处理模式
- 对每个来源分别应用历史确认

```python
def apply_historical_confirmations(self):
    """手动应用历史确认记录（支持批量模式）"""
    # 检查是否为批量处理模式
    if self.current_case_name == "回归批量处理":
        # 批量处理模式：对每个来源分别应用历史确认
        violations_by_source = {}
        # ... 分组处理逻辑
        
        total_applied = 0
        for source_key, source_violations in violations_by_source.items():
            case_name, corner_name = source_key.split('_', 1)
            
            # 对每个来源应用历史确认
            applied_count = self.data_model.apply_historical_confirmations(
                case_name, corner_name
            )
            total_applied += applied_count
```

## 测试验证

创建了测试脚本`test_batch_fixes.py`验证修复效果：

### 测试结果
```
=== 测试结果 ===
缓存失效功能: ✓
过滤器选择功能: ✓
所有测试通过！
```

## 用户体验改进

1. **缓存智能化**：自动检测目录变化，确保新增用例及时显示
2. **过滤精确化**：选择操作严格遵循过滤器设置，避免误选
3. **批量处理可靠化**：历史确认功能在批量模式下正常工作

## 注意事项

1. 目录修改时间检查可能在某些文件系统上不够精确，但已经能覆盖大部分使用场景
2. 批量处理时会先清除旧数据再保存新数据，确保数据一致性
3. 分组标识行在处理时会被正确跳过，避免干扰业务逻辑

## 后续优化建议

1. 可以考虑增加文件内容哈希检查，进一步提高缓存精确度
2. 可以优化批量处理的进度显示，让用户更清楚当前处理状态
3. 可以增加批量处理的错误恢复机制，提高健壮性