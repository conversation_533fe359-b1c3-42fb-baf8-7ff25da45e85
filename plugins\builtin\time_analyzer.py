from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                           QFileDialog, QLabel, QToolButton, QComboBox, QLineEdit,
                           QProgressBar, QApplication, QStyledItemDelegate)
from PyQt5.QtCore import Qt, QObject, pyqtSignal, QThread
from PyQt5.QtGui import QColor, QFont
import os
import re
import json
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime
from plugins.base import PluginBase
from abc import ABC
from utils.time_unit_converter import TimeUnitConverter, TimeUnit

# 创建一个新的元类来解决冲突
class TimeAnalyzerMeta(type(QObject), type(ABC)):
    pass


class TimeDisplayDelegate(QStyledItemDelegate):
    """自定义委托，用于正确显示时间格式"""
    
    def __init__(self, time_unit_combo, parent=None):
        super().__init__(parent)
        self.time_unit_combo = time_unit_combo
    
    def displayText(self, value, locale):
        """自定义显示文本"""
        try:
            # 如果是数值类型，格式化显示
            if isinstance(value, (int, float)) and value >= 0:
                current_unit = self.time_unit_combo.currentData()
                if current_unit is None:
                    current_unit = TimeUnit.MINUTES
                return TimeUnitConverter.format_time(value, current_unit, False)
            return str(value)
        except Exception:
            return str(value)


class AnalysisThread(QThread):
    """时间分析线程"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    analysis_completed = pyqtSignal(list)  # 分析完成信号
    analysis_failed = pyqtSignal(str)  # 分析失败信号

    def __init__(self, analyzer, analysis_dir):
        super().__init__()
        self.analyzer = analyzer
        self.analysis_dir = analysis_dir

    def run(self):
        """运行分析"""
        try:
            self.progress_updated.emit("开始扫描目录...")

            # 获取所有用例目录
            case_dirs = []
            for item in os.listdir(self.analysis_dir):
                case_path = os.path.join(self.analysis_dir, item)
                if os.path.isdir(case_path):
                    log_dir = os.path.join(case_path, "log")
                    if os.path.exists(log_dir):
                        case_dirs.append(item)

            if not case_dirs:
                self.analysis_failed.emit("未找到任何包含日志的用例目录")
                return

            self.progress_updated.emit(f"找到 {len(case_dirs)} 个用例目录，开始分析...")

            time_data = []
            for i, case_dir in enumerate(case_dirs):
                # 更新进度
                progress = f"正在分析 {case_dir} ({i+1}/{len(case_dirs)})"
                self.progress_updated.emit(progress)

                case_path = os.path.join(self.analysis_dir, case_dir)
                log_dir = os.path.join(case_path, "log")

                # 查找编译日志和仿真日志
                compile_log = os.path.join(log_dir, "irun_compile.log")
                sim_log = os.path.join(log_dir, "irun_sim.log")

                # 解析时间和内存
                compile_time = self.analyzer.get_time_from_log(compile_log, False)
                sim_time = self.analyzer.get_time_from_log(sim_log, True)
                compile_memory = self.analyzer.get_memory_from_log(compile_log, False)
                sim_memory = self.analyzer.get_memory_from_log(sim_log, True)

                # 如果两个日志都不存在，跳过该用例
                if compile_time is None and sim_time is None:
                    continue

                # 计算总时间
                total_time = 0
                if compile_time:
                    total_time += compile_time
                if sim_time:
                    total_time += sim_time

                # 添加到数据列表
                time_data.append({
                    'case': case_dir,
                    'compile': compile_time or 0,
                    'sim': sim_time or 0,
                    'total': total_time,
                    'compile_memory': compile_memory or 0,
                    'sim_memory': sim_memory or 0
                })

            if not time_data:
                self.analysis_failed.emit("未找到任何有效的时间数据")
                return

            # 按总时间排序
            time_data.sort(key=lambda x: x['total'], reverse=True)

            self.progress_updated.emit("分析完成！")
            self.analysis_completed.emit(time_data)

        except Exception as e:
            self.analysis_failed.emit(f"分析过程中发生错误：{str(e)}")

# 使用新的元类
class TimeAnalyzerPlugin(QObject, PluginBase, metaclass=TimeAnalyzerMeta):
    # 定义信号
    task_progress = pyqtSignal(str)
    task_completed = pyqtSignal()

    def __init__(self):
        QObject.__init__(self)
        PluginBase.__init__(self)
        # 对话框引用
        self.current_dialog = None
        # 分析线程
        self.analysis_thread = None

    @property
    def name(self):
        return "仿真时间分析器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "统计所有用例的编译和仿真时间"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.analyze_time)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton

            # 创建状态指示器标签
            self.status_label = QLabel("仿真时间分析器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("仿真时间分析器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def get_time_from_log(self, log_path, is_sim_log=False):
        """从日志文件中提取时间信息

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志，用于区分编译和仿真日志格式
        """
        try:
            if not os.path.exists(log_path):
                return None

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # 尝试匹配xrun格式
                xrun_pattern = r'xrun: Time - (\d+\.?\d*)s'
                if match := re.search(xrun_pattern, content):
                    seconds = float(match.group(1))
                    return round(seconds / 60, 2)

                # 尝试匹配VCS格式
                if "Compilation Performance Summary" in content:
                    if is_sim_log:
                        # 对于仿真日志，先定位到仿真部分
                        if "SimuLation Performance Summary" in content:
                            # 分割内容，只保留仿真部分
                            sim_part = content.split("SimuLation Performance Summary")[-1]
                            # 在仿真部分中查找Elapsed Time
                            sim_pattern = r'Elapsed Time\s+:\s+(\d+)\s+sec'
                            if sim_match := re.search(sim_pattern, sim_part):
                                sim_time = float(sim_match.group(1))
                                return round(sim_time / 60, 2)
                    else:
                        # 编译日志，查找第一个Elapsed time
                        compile_pattern = r'Elapsed time\s+:\s+(\d+)\s+sec'
                        if match := re.search(compile_pattern, content):
                            compile_time = float(match.group(1))
                            return round(compile_time / 60, 2)

        except Exception as e:
            print(f"读取日志 {log_path} 失败: {str(e)}")
        return None

    def get_memory_from_log(self, log_path, is_sim_log=False):
        """从日志文件中提取内存使用信息

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志，用于区分编译和仿真日志格式
        
        Returns:
            float: 内存使用量(MB)，如果未找到则返回None
        """
        try:
            if not os.path.exists(log_path):
                return None

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                if is_sim_log:
                    # 仿真日志：查找 xmsim: Memory Usage - Final: XXX.XM
                    sim_memory_pattern = r'xmsim: Memory Usage - Final:\s*(\d+\.?\d*)M'
                    if match := re.search(sim_memory_pattern, content):
                        memory_mb = float(match.group(1))
                        return round(memory_mb, 1)
                else:
                    # 编译日志：查找 xmelab: Memory Usage - Final:XXX.XM
                    compile_memory_pattern = r'xmelab: Memory Usage - Final:\s*(\d+\.?\d*)M'
                    if match := re.search(compile_memory_pattern, content):
                        memory_mb = float(match.group(1))
                        return round(memory_mb, 1)

        except Exception as e:
            print(f"读取内存信息 {log_path} 失败: {str(e)}")
        return None

    def analyze_time(self):
        """显示时间分析界面"""
        try:
            # 直接显示分析界面，不自动开始分析
            self.show_analyzer_dialog()

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"打开时间分析器失败：{str(e)}")

    def get_default_analysis_dir(self):
        """获取默认分析目录"""
        # 优先使用环境变量 PROJ_WORK
        proj_work = os.environ.get('PROJ_WORK')
        if proj_work and os.path.exists(proj_work):
            return proj_work

        # 备选：当前工作目录
        return os.getcwd()



    def export_to_excel(self, table):
        """将表格数据导出为Excel文件"""
        try:
            # 获取保存路径
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"simulation_time_{current_time}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self.main_window,
                "导出Excel",
                default_name,
                "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 创建工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "仿真时间统计"

            # 写入表头
            headers = ["用例名称", "编译时间(分钟)", "仿真时间(分钟)", "总时间(分钟)", "编译内存(MB)", "仿真内存(MB)"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col)
                cell.value = header
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # 写入数据
            for row in range(table.rowCount()):
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        cell = ws.cell(row=row + 2, column=col + 1)
                        cell.value = item.text()
                        # 对齐方式
                        if col == 0:  # 用例名称列左对齐
                            cell.alignment = Alignment(horizontal="left", vertical="center")
                        else:  # 时间列右对齐
                            cell.alignment = Alignment(horizontal="right", vertical="center")
                        # 总计行加粗
                        if row == table.rowCount() - 1:
                            cell.font = Font(bold=True)
                            cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")

            # 调整列宽
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width

            # 保存文件
            wb.save(file_path)
            QMessageBox.information(self.main_window, "成功", "数据已成功导出到Excel文件")

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"导出Excel失败：{str(e)}")

    def show_analyzer_dialog(self):
        """显示时间分析器对话框"""
        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("仿真时间分析器")
        dialog.resize(900, 700)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        # 连接信号
        self.task_progress.connect(lambda msg: self.main_window.show_message(msg, 3000))
        self.task_completed.connect(lambda: self.main_window.show_message("时间分析完成", 3000))

        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(10, 10, 10, 10)

        # 添加目录选择区域
        dir_layout = QHBoxLayout()
        dir_label = QLabel("分析目录:")
        dir_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        # 目录输入框
        self.dir_input = QLineEdit()
        default_dir = self.get_default_analysis_dir()
        self.dir_input.setText(default_dir)
        self.dir_input.setPlaceholderText("选择要分析的目录...")

        # 浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_directory)
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        # 开始分析按钮
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.clicked.connect(self.start_manual_analysis)
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 5px 20px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)

        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_input, stretch=1)
        dir_layout.addWidget(browse_btn)
        dir_layout.addWidget(self.analyze_btn)
        layout.addLayout(dir_layout)

        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 2px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 添加状态标签
        self.status_label = QLabel("请选择分析目录并点击'开始分析'按钮")
        self.status_label.setStyleSheet("color: #7f8c8d; font-style: italic; margin: 5px;")
        layout.addWidget(self.status_label)

        # 添加时间单位选择控件
        unit_layout = QHBoxLayout()
        unit_label = QLabel("时间单位:")
        unit_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.time_unit_combo = QComboBox()
        for unit in TimeUnitConverter.get_all_units():
            display_name = TimeUnitConverter.get_unit_display_name(unit)
            self.time_unit_combo.addItem(display_name, unit)

        # 默认选择分钟
        self.time_unit_combo.setCurrentIndex(0)
        # 使用更安全的信号连接方式
        self.time_unit_combo.currentIndexChanged.connect(self.on_time_unit_changed)
        self.time_unit_combo.setEnabled(False)  # 初始禁用，直到有数据

        unit_layout.addWidget(unit_label)
        unit_layout.addWidget(self.time_unit_combo)
        unit_layout.addStretch()
        layout.addLayout(unit_layout)

        # 创建表格
        self.time_table = QTableWidget()
        self.time_table.setColumnCount(6)
        self.time_table.setRowCount(0)

        # 设置初始表头
        headers = ["用例名称", "编译时间(分钟)", "仿真时间(分钟)", "总时间(分钟)", "编译内存(MB)", "仿真内存(MB)"]
        self.time_table.setHorizontalHeaderLabels(headers)

        # 存储原始时间数据（以分钟为单位）
        self.original_time_data = []

        # 创建并设置时间显示委托（用于时间列）
        self.time_delegate = TimeDisplayDelegate(self.time_unit_combo)
        for col in range(1, 4):  # 列1,2,3是时间列
            self.time_table.setItemDelegateForColumn(col, self.time_delegate)

        # 设置表格样式
        self.setup_table_style()

        # 将表格添加到布局
        layout.addWidget(self.time_table)

        # 保存列宽到设置
        def save_column_widths():
            widths = [self.time_table.columnWidth(i) for i in range(self.time_table.columnCount())]
            settings = {
                "column_widths": widths
            }
            try:
                with open("time_analyzer_settings.json", "w") as f:
                    json.dump(settings, f)
            except Exception as e:
                print(f"保存列宽设置失败: {str(e)}")

        # 从设置加载列宽
        try:
            with open("time_analyzer_settings.json", "r") as f:
                settings = json.load(f)
                widths = settings.get("column_widths", [])
                if len(widths) == self.time_table.columnCount():
                    for i, width in enumerate(widths):
                        self.time_table.setColumnWidth(i, width)
        except Exception:
            pass  # 如果设置文件不存在或无效，使用默认列宽

        # 在对话框关闭时保存列宽
        dialog.finished.connect(save_column_widths)

        # 在布局底部添加按钮区域
        button_layout = QHBoxLayout()

        # 添加导出按钮
        self.export_button = QPushButton("导出到Excel")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.export_button.clicked.connect(lambda: self.export_to_excel(self.time_table))
        self.export_button.setEnabled(False)  # 初始禁用，直到有数据
        button_layout.addWidget(self.export_button)

        button_layout.addStretch()

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
            QPushButton:pressed {
                background-color: #495057;
            }
        """)
        close_button.clicked.connect(dialog.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        # 设置对话框布局
        dialog.setLayout(layout)

        # 显示对话框
        dialog.show()

    def setup_table_style(self):
        """设置表格样式"""
        # 设置表格样式
        self.time_table.setAlternatingRowColors(True)
        self.time_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.time_table.setSelectionMode(QTableWidget.SingleSelection)
        self.time_table.setSortingEnabled(True)

        # 设置表头样式
        header = self.time_table.horizontalHeader()
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: 1px solid #2980b9;
                font-weight: bold;
            }
        """)

        # 设置列宽
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 用例名称列可手动调整
        for i in range(1, 6):
            header.setSectionResizeMode(i, QHeaderView.Interactive)  # 时间和内存列可手动调整

        # 设置初始列宽
        self.time_table.setColumnWidth(0, 300)  # 用例名称列宽
        self.time_table.setColumnWidth(1, 120)  # 编译时间列宽
        self.time_table.setColumnWidth(2, 120)  # 仿真时间列宽
        self.time_table.setColumnWidth(3, 120)  # 总时间列宽
        self.time_table.setColumnWidth(4, 120)  # 编译内存列宽
        self.time_table.setColumnWidth(5, 120)  # 仿真内存列宽

        # 设置行高
        self.time_table.verticalHeader().setDefaultSectionSize(30)
        self.time_table.verticalHeader().setVisible(False)

        # 设置表格样式
        self.time_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

    def browse_directory(self):
        """浏览选择分析目录"""
        try:
            current_dir = self.dir_input.text() or self.get_default_analysis_dir()
            selected_dir = QFileDialog.getExistingDirectory(
                self.current_dialog,
                "选择分析目录",
                current_dir
            )

            if selected_dir:
                self.dir_input.setText(selected_dir)
                self.status_label.setText(f"已选择目录: {selected_dir}")

        except Exception as e:
            QMessageBox.warning(self.current_dialog, "警告", f"选择目录失败: {str(e)}")

    def start_manual_analysis(self):
        """开始手动分析"""
        try:
            analysis_dir = self.dir_input.text().strip()

            if not analysis_dir:
                QMessageBox.warning(self.current_dialog, "警告", "请先选择分析目录")
                return

            if not os.path.exists(analysis_dir):
                QMessageBox.warning(self.current_dialog, "警告", "选择的目录不存在")
                return

            if not os.path.isdir(analysis_dir):
                QMessageBox.warning(self.current_dialog, "警告", "选择的路径不是目录")
                return

            # 禁用分析按钮，显示进度条
            self.analyze_btn.setEnabled(False)
            self.analyze_btn.setText("分析中...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.status_label.setText("正在分析，请稍候...")

            # 暂时禁用时间单位选择，防止在分析过程中切换
            self.time_unit_combo.setEnabled(False)

            # 创建并启动分析线程
            self.analysis_thread = AnalysisThread(self, analysis_dir)
            self.analysis_thread.progress_updated.connect(self.on_analysis_progress)
            self.analysis_thread.analysis_completed.connect(self.on_analysis_completed)
            self.analysis_thread.analysis_failed.connect(self.on_analysis_failed)
            self.analysis_thread.start()

        except Exception as e:
            QMessageBox.critical(self.current_dialog, "错误", f"启动分析失败: {str(e)}")
            self.reset_analysis_ui()

    def on_analysis_progress(self, message):
        """分析进度更新"""
        self.status_label.setText(message)
        QApplication.processEvents()  # 更新UI

    def on_analysis_completed(self, time_data):
        """分析完成"""
        try:
            self.reset_analysis_ui()

            if not time_data:
                QMessageBox.information(self.current_dialog, "提示", "未找到任何有效的时间数据")
                # 清空数据并禁用控件
                self.original_time_data = []
                self.time_unit_combo.setEnabled(False)
                self.export_button.setEnabled(False)
                return

            # 保存数据并更新显示
            self.original_time_data = time_data
            self.update_time_display(preserve_sorting=False)

            # 启用相关控件
            self.time_unit_combo.setEnabled(True)
            self.export_button.setEnabled(True)

            self.status_label.setText(f"分析完成！找到 {len(time_data)} 个用例的时间数据")

        except Exception as e:
            QMessageBox.critical(self.current_dialog, "错误", f"处理分析结果失败: {str(e)}")
            # 发生错误时重置状态
            self.original_time_data = []
            self.time_unit_combo.setEnabled(False)
            self.export_button.setEnabled(False)

    def on_analysis_failed(self, error_message):
        """分析失败"""
        self.reset_analysis_ui()
        # 分析失败时禁用相关控件
        self.time_unit_combo.setEnabled(False)
        self.export_button.setEnabled(False)
        QMessageBox.warning(self.current_dialog, "分析失败", error_message)

    def reset_analysis_ui(self):
        """重置分析界面状态"""
        self.analyze_btn.setEnabled(True)
        self.analyze_btn.setText("开始分析")
        self.progress_bar.setVisible(False)
        # 注意：不要在这里清空 original_time_data，保持数据持久性

    def update_time_display(self, preserve_sorting=False):
        """更新时间显示（根据当前选择的时间单位）
        
        Args:
            preserve_sorting: 是否保持当前的排序状态，True时只更新数值不重新填充表格
        """
        try:
            # 检查数据是否存在
            if not hasattr(self, 'original_time_data') or not self.original_time_data:
                print("update_time_display: 没有原始时间数据")
                return

            # 检查控件是否存在
            if not hasattr(self, 'time_unit_combo') or not hasattr(self, 'time_table'):
                print("update_time_display: 必要的UI控件不存在")
                return

            # 获取当前选择的时间单位
            current_unit = self.time_unit_combo.currentData()
            if current_unit is None:
                current_unit = TimeUnit.MINUTES
                print(f"update_time_display: 时间单位为None，使用默认值: {current_unit}")
            else:
                print(f"update_time_display: 当前时间单位: {current_unit}")

            print(f"update_time_display: 开始更新显示，数据条数: {len(self.original_time_data)}, 保持排序: {preserve_sorting}")

            # 更新表头
            headers = [
                "用例名称",
                TimeUnitConverter.get_table_header("编译时间", current_unit),
                TimeUnitConverter.get_table_header("仿真时间", current_unit),
                TimeUnitConverter.get_table_header("总时间", current_unit),
                "编译内存(MB)",
                "仿真内存(MB)"
            ]
            self.time_table.setHorizontalHeaderLabels(headers)

            if preserve_sorting and self.time_table.rowCount() > 0:
                # 保持排序状态，只更新现有表格项的数值
                self.update_existing_table_values(current_unit)
            else:
                # 重新填充整个表格
                self.populate_table_from_scratch(current_unit)

            print(f"update_time_display: 显示更新完成，当前单位: {current_unit}")

        except Exception as e:
            print(f"更新时间显示失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_existing_table_values(self, current_unit):
        """更新现有表格项的数值，保持当前排序"""
        try:
            # 创建用例名称到数据的映射
            case_data_map = {data['case']: data for data in self.original_time_data}
            
            # 计算总计
            total_compile = sum(data.get('compile', 0) or 0 for data in self.original_time_data)
            total_sim = sum(data.get('sim', 0) or 0 for data in self.original_time_data)
            total_time = sum(data.get('total', 0) or 0 for data in self.original_time_data)
            total_compile_memory = sum(data.get('compile_memory', 0) or 0 for data in self.original_time_data)
            total_sim_memory = sum(data.get('sim_memory', 0) or 0 for data in self.original_time_data)
            
            # 遍历表格中的每一行，根据用例名称更新对应的时间数据
            for row in range(self.time_table.rowCount()):
                name_item = self.time_table.item(row, 0)
                if not name_item:
                    continue
                    
                case_name = name_item.text()
                
                # 如果是总计行
                if case_name == "总计":
                    # 更新总计行
                    total_compile_converted = TimeUnitConverter.convert_time(total_compile, TimeUnit.MINUTES, current_unit)
                    total_sim_converted = TimeUnitConverter.convert_time(total_sim, TimeUnit.MINUTES, current_unit)
                    total_time_converted = TimeUnitConverter.convert_time(total_time, TimeUnit.MINUTES, current_unit)
                    
                    totals = [total_compile_converted, total_sim_converted, total_time_converted, total_compile_memory, total_sim_memory]
                    for col, total in enumerate(totals, 1):
                        item = self.time_table.item(row, col)
                        if item:
                            if total is not None and total >= 0:
                                item.setData(Qt.DisplayRole, float(total))
                            else:
                                item.setData(Qt.DisplayRole, 0.0)
                    continue
                
                # 查找对应的数据
                if case_name in case_data_map:
                    data = case_data_map[case_name]
                    
                    # 获取原始时间和内存数据
                    compile_raw = data.get('compile', 0) or 0
                    sim_raw = data.get('sim', 0) or 0
                    total_raw = data.get('total', 0) or 0
                    compile_memory = data.get('compile_memory', 0) or 0
                    sim_memory = data.get('sim_memory', 0) or 0
                    
                    # 转换时间单位
                    compile_time = TimeUnitConverter.convert_time(compile_raw, TimeUnit.MINUTES, current_unit)
                    sim_time = TimeUnitConverter.convert_time(sim_raw, TimeUnit.MINUTES, current_unit)
                    total_case_time = TimeUnitConverter.convert_time(total_raw, TimeUnit.MINUTES, current_unit)
                    
                    # 更新时间和内存列的数值
                    values = [compile_time, sim_time, total_case_time, compile_memory, sim_memory]
                    for col, val in enumerate(values, 1):
                        item = self.time_table.item(row, col)
                        if item:
                            if val is not None and val >= 0:
                                item.setData(Qt.DisplayRole, float(val))
                            else:
                                item.setData(Qt.DisplayRole, 0.0)
                                
        except Exception as e:
            print(f"更新现有表格数值失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def populate_table_from_scratch(self, current_unit):
        """从头填充表格数据"""
        try:
            # 清空表格内容
            self.time_table.setRowCount(0)
            self.time_table.setRowCount(len(self.original_time_data) + 1)  # +1 for total row

            # 填充数据
            total_compile = 0
            total_sim = 0
            total_time = 0
            total_compile_memory = 0
            total_sim_memory = 0

            for row, data in enumerate(self.original_time_data):
                try:
                    # 验证数据完整性
                    if not isinstance(data, dict) or 'case' not in data:
                        print(f"populate_table_from_scratch: 第{row}行数据格式错误: {data}")
                        continue

                    # 获取原始时间和内存数据（确保为数字类型）
                    compile_raw = data.get('compile', 0) or 0
                    sim_raw = data.get('sim', 0) or 0
                    total_raw = data.get('total', 0) or 0
                    compile_memory = data.get('compile_memory', 0) or 0
                    sim_memory = data.get('sim_memory', 0) or 0

                    # 转换时间单位
                    compile_time = TimeUnitConverter.convert_time(compile_raw, TimeUnit.MINUTES, current_unit)
                    sim_time = TimeUnitConverter.convert_time(sim_raw, TimeUnit.MINUTES, current_unit)
                    total_case_time = TimeUnitConverter.convert_time(total_raw, TimeUnit.MINUTES, current_unit)

                    # 累计总时间和内存（使用原始数据）
                    total_compile += compile_raw
                    total_sim += sim_raw
                    total_time += total_raw
                    total_compile_memory += compile_memory
                    total_sim_memory += sim_memory

                    # 设置用例名称
                    name_item = QTableWidgetItem(str(data['case']))
                    name_item.setToolTip(str(data['case']))
                    self.time_table.setItem(row, 0, name_item)

                    # 设置时间和内存数据（右对齐）
                    values = [compile_time, sim_time, total_case_time, compile_memory, sim_memory]
                    for col, val in enumerate(values, 1):
                        try:
                            item = QTableWidgetItem()
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                            
                            # 直接设置数值，委托会处理显示格式
                            if val is not None and val >= 0:
                                item.setData(Qt.DisplayRole, float(val))
                            else:
                                item.setData(Qt.DisplayRole, 0.0)
                            
                            self.time_table.setItem(row, col, item)
                        except Exception as col_e:
                            print(f"populate_table_from_scratch: 设置第{row}行第{col}列失败: {str(col_e)}")
                            # 设置默认值
                            item = QTableWidgetItem()
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                            item.setData(Qt.DisplayRole, 0.0)
                            self.time_table.setItem(row, col, item)

                except Exception as row_e:
                    print(f"populate_table_from_scratch: 处理第{row}行数据失败: {str(row_e)}")
                    continue

            # 添加总计行
            total_row = len(self.original_time_data)

            try:
                # 转换总计时间单位
                total_compile_converted = TimeUnitConverter.convert_time(total_compile, TimeUnit.MINUTES, current_unit)
                total_sim_converted = TimeUnitConverter.convert_time(total_sim, TimeUnit.MINUTES, current_unit)
                total_time_converted = TimeUnitConverter.convert_time(total_time, TimeUnit.MINUTES, current_unit)

                total_label = QTableWidgetItem("总计")
                total_label.setFont(QFont("", -1, QFont.Bold))
                total_label.setBackground(QColor("#f8f9fa"))
                self.time_table.setItem(total_row, 0, total_label)

                totals = [total_compile_converted, total_sim_converted, total_time_converted, total_compile_memory, total_sim_memory]
                for col, total in enumerate(totals, 1):
                    try:
                        total_item = QTableWidgetItem()
                        total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        total_item.setFont(QFont("", -1, QFont.Bold))
                        total_item.setBackground(QColor("#f8f9fa"))
                        
                        # 直接设置数值，委托会处理显示格式
                        if total is not None and total >= 0:
                            total_item.setData(Qt.DisplayRole, float(total))
                        else:
                            total_item.setData(Qt.DisplayRole, 0.0)
                            
                        self.time_table.setItem(total_row, col, total_item)
                    except Exception as total_col_e:
                        print(f"populate_table_from_scratch: 设置总计行第{col}列失败: {str(total_col_e)}")

            except Exception as total_e:
                print(f"populate_table_from_scratch: 添加总计行失败: {str(total_e)}")
                
        except Exception as e:
            print(f"从头填充表格失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_time_unit_changed(self):
        """时间单位变化处理"""
        try:
            print("on_time_unit_changed: 时间单位发生变化")
            # 检查是否有数据
            if hasattr(self, 'original_time_data') and self.original_time_data:
                print(f"on_time_unit_changed: 有数据，开始更新显示，数据条数: {len(self.original_time_data)}")
                # 保持当前的排序状态
                self.update_time_display(preserve_sorting=True)
                # 强制刷新表格显示
                if hasattr(self, 'time_table'):
                    self.time_table.viewport().update()
            else:
                print("on_time_unit_changed: 没有数据，跳过更新")
        except Exception as e:
            print(f"时间单位变化处理失败: {str(e)}")
            import traceback
            traceback.print_exc()

